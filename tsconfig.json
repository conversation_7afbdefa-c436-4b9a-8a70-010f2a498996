{"compilerOptions": {"composite": true, "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.tsbuildinfo", "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "module": "es6", "target": "es5", "lib": ["es6", "dom"], "sourceMap": true, "baseUrl": "./", "types": ["vite/types/importMeta"], "paths": {"*": ["node_modules/*", "src/types/*"]}}, "files": [], "references": [{"path": "./tsconfig.node.json"}, {"path": "./tsconfig.app.json"}]}