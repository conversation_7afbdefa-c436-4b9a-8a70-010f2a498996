# AI审方系统 - 密码管理功能

## 功能概述

为AI审方系统新增了完整的密码管理功能，包括：

1. **修改密码功能** (`/change-password`)
2. **忘记密码功能** (`/forgot-password`)
3. **登录界面增强** - 添加了密码管理入口

## 新增文件

### 1. 页面组件
- `src/views/ChangePassword.vue` - 修改密码界面
- `src/views/ForgotPassword.vue` - 忘记密码界面

### 2. API接口
- `src/api/module/authApi.ts` - 新增修改密码和忘记密码接口

### 3. 路由配置
- 更新 `src/router/index.ts` - 添加新页面路由

## 功能特性

### 修改密码界面特性
- ✅ 用户名输入验证
- ✅ 原密码输入
- ✅ 新密码输入（包含强度验证）
- ✅ 确认新密码（一致性验证）
- ✅ 验证码验证
- ✅ 表单验证和错误提示
- ✅ 响应式设计

### 忘记密码界面特性
- ✅ 用户名输入验证
- ✅ 邮箱地址输入验证
- ✅ 验证码验证
- ✅ 发送重置链接功能
- ✅ 表单验证和错误提示
- ✅ 响应式设计

### 登录界面增强
- ✅ 添加"修改密码"入口
- ✅ 添加"忘记密码"入口
- ✅ 美观的链接样式设计

## 路由配置

```javascript
// 新增路由
/change-password    - 修改密码页面（公开访问）
/forgot-password    - 忘记密码页面（公开访问）

// 现有路由
/login             - 登录页面（公开访问）
/main              - 主布局（需要登录）
  ├── home         - 首页
  ├── detail/:id   - 详情页
  └── progress/:id - 进度页
```

## 使用方法

### 1. 修改密码
1. 在登录页面点击"修改密码"链接
2. 输入用户名和原密码
3. 输入新密码（需满足强度要求）
4. 确认新密码
5. 输入验证码
6. 点击"确认修改"按钮

### 2. 忘记密码
1. 在登录页面点击"忘记密码"链接
2. 输入用户名和邮箱地址
3. 输入验证码
4. 点击"发送重置链接"按钮
5. 查收邮箱中的重置链接

### 3. 页面导航
- 各页面之间可以通过底部链接相互跳转
- 所有页面都可以返回登录页面

## API接口

### 修改密码接口
```typescript
POST /v0-1/auth/change-password
{
  username: string,
  oldPassword: string,
  newPassword: string,
  captcha: string
}
```

### 忘记密码接口
```typescript
POST /v0-1/auth/forgot-password
{
  username: string,
  email: string,
  captcha: string
}
```

## 安全特性

### 密码强度验证
- 最少6个字符
- 必须包含大写字母
- 必须包含小写字母
- 必须包含数字
- 支持特殊字符

### 验证码保护
- 所有密码相关操作都需要验证码
- 验证码可点击刷新
- 不区分大小写验证

### 表单验证
- 实时输入验证
- 详细错误提示
- 防止重复提交

## 样式设计

### 设计一致性
- 与登录界面保持一致的设计风格
- 使用相同的渐变背景和卡片布局
- 统一的按钮和输入框样式

### 响应式设计
- 支持移动端适配
- 灵活的布局调整
- 优化的触摸体验

### 用户体验
- 平滑的过渡动画
- 直观的操作流程
- 清晰的状态反馈

## 开发说明

### 1. 修改API地址
在 `src/api/module/authApi.ts` 中修改接口地址：
```typescript
// 修改密码
export const changePassword = (params: ChangePasswordParams): Promise<any> => {
  return request({
    url: "/v0-1/auth/change-password", // 修改此处
    method: "post",
    data: params,
  });
};
```

### 2. 自定义验证规则
在各页面组件中修改验证规则：
```typescript
const changePasswordRules = reactive({
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' },
    { 
      pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{6,}$/, 
      message: '密码必须包含大小写字母和数字', 
      trigger: 'blur' 
    }
  ]
})
```

### 3. 修改页面样式
在各组件的 `<style>` 部分修改样式

### 4. 集成邮件服务
忘记密码功能需要配置邮件服务：
- 配置SMTP服务器
- 设置邮件模板
- 处理邮件发送逻辑

## 测试说明

目前所有功能使用模拟数据：
- 任意符合格式要求的输入都可以成功
- 验证码需要正确输入（不区分大小写）
- 实际部署时需要连接真实的后端API

## 注意事项

1. **安全性**: 生产环境需要加强密码传输加密
2. **邮件服务**: 忘记密码功能需要配置邮件服务
3. **验证码**: 建议集成图形验证码或短信验证码
4. **日志记录**: 建议记录密码修改操作日志
5. **频率限制**: 建议添加操作频率限制防止滥用

## 下一步优化建议

1. 集成真实的邮件服务
2. 添加短信验证码选项
3. 实现密码历史记录防重复
4. 添加密码过期提醒功能
5. 集成双因素认证(2FA)
6. 优化错误处理和用户提示
