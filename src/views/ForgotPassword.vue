<template>
  <div class="forgot-password-container">
    <div class="forgot-password-box">
      <div class="forgot-password-header">
        <img src="@/assets/images/logo.png" alt="logo" class="logo" />
        <h1 class="title">忘记密码</h1>
        <p class="subtitle">请输入您的用户名和邮箱，我们将发送重置密码链接</p>
      </div>

      <el-form
        ref="forgotPasswordFormRef"
        :model="forgotPasswordForm"
        :rules="forgotPasswordRules"
        class="forgot-password-form"
        @keyup.enter="handleForgotPassword"
      >
        <el-form-item prop="username">
          <el-input
            v-model="forgotPasswordForm.username"
            placeholder="请输入用户名"
            size="large"
            prefix-icon="User"
            clearable
          />
        </el-form-item>

        <el-form-item prop="email">
          <el-input
            v-model="forgotPasswordForm.email"
            placeholder="请输入邮箱地址"
            size="large"
            prefix-icon="Message"
            clearable
          />
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            size="large"
            class="forgot-password-btn"
            :loading="loading"
            @click="handleForgotPassword"
          >
            {{ loading ? "发送中..." : "发送重置链接" }}
          </el-button>
        </el-form-item>
      </el-form>

      <!-- 功能链接 -->
      <div class="forgot-password-links">
        <span @click="gotoLogin" class="link-btn">
          返回登录
        </span>
        <span class="divider">|</span>
        <span @click="gotoChangePassword" class="link-btn">
          修改密码
        </span>
      </div>

      <div class="forgot-password-footer">
        <p>© 2025 AI审方系统 - 智能药物审查解决方案</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive, ref } from "vue";
import { ElMessage } from "element-plus";
import router from "@/router";
import api from "@/api";

// 表单引用
const forgotPasswordFormRef = ref();

// 加载状态
const loading = ref(false);

// 验证码
const captchaCode = ref("");

// 忘记密码表单数据
const forgotPasswordForm = reactive({
  username: "",
  email: "",
  captcha: "",
});

// 表单验证规则
const forgotPasswordRules = reactive({
  username: [
    { required: true, message: "请输入用户名", trigger: "blur" },
    {
      min: 3,
      max: 20,
      message: "用户名长度在 3 到 20 个字符",
      trigger: "blur",
    },
  ],
  email: [
    { required: true, message: "请输入邮箱地址", trigger: "blur" },
    { type: "email", message: "请输入正确的邮箱地址", trigger: "blur" },
  ],
  captcha: [
    { required: true, message: "请输入验证码", trigger: "blur" },
    { len: 4, message: "验证码长度为 4 位", trigger: "blur" },
  ],
});

// 生成随机验证码
const generateCaptcha = () => {
  const chars = "ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678";
  let result = "";
  for (let i = 0; i < 4; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
};

// 刷新验证码
const refreshCaptcha = () => {
  captchaCode.value = generateCaptcha();
};

// 处理忘记密码
const handleForgotPassword = async () => {
  if (!forgotPasswordFormRef.value) return;

  try {
    // 验证表单
    await forgotPasswordFormRef.value.validate();

    // 验证验证码
    if (
      forgotPasswordForm.captcha.toLowerCase() !==
      captchaCode.value.toLowerCase()
    ) {
      ElMessage.error("验证码错误");
      refreshCaptcha();
      return;
    }

    loading.value = true;

    // 调用忘记密码API
    const response = await api.forgotPassword({
      username: forgotPasswordForm.username,
      email: forgotPasswordForm.email,
      captcha: forgotPasswordForm.captcha,
    });

    if (response.code === 200) {
      ElMessage.success("重置密码链接已发送到您的邮箱，请查收");
      // 跳转到登录页面
      router.push({ name: "login" });
    } else {
      ElMessage.error(response.message || "发送失败");
      refreshCaptcha();
    }
  } catch (error) {
    console.error("发送重置链接失败:", error);
    ElMessage.error("发送失败，请检查输入信息");
    refreshCaptcha();
  } finally {
    loading.value = false;
  }
};

// 跳转到登录页面
const gotoLogin = () => {
  router.push({ name: "login" });
};

// 跳转到修改密码页面
const gotoChangePassword = () => {
  router.push({ name: "changePassword" });
};

// 初始化验证码
onMounted(() => {
  refreshCaptcha();
});
</script>

<style lang="scss" scoped>
.forgot-password-container {
  height: 100vh;
  width: 100vw;
  background: url("@/assets/images/login_mainbg.jpg") 0 0 no-repeat;
  background-size: cover;
  // background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  box-sizing: border-box;
}

.forgot-password-box {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 40px;
  width: 100%;
  max-width: 420px;
  min-height: 500px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.forgot-password-header {
  text-align: center;
  margin-bottom: 40px;

  .logo {
    width: 80px;
    height: 80px;
    margin-bottom: 20px;
  }

  .title {
    font-size: 28px;
    font-weight: bold;
    color: #333;
    margin: 0 0 8px 0;
  }

  .subtitle {
    font-size: 14px;
    color: #666;
    margin: 0;
    line-height: 1.4;
  }
}

.forgot-password-form {
  .el-form-item {
    margin-bottom: 24px;
  }

  .captcha-container {
    display: flex;
    gap: 12px;

    .captcha-input {
      flex: 1;
    }

    .captcha-code {
      width: 100px;
      height: 40px;
      background: linear-gradient(45deg, #f0f0f0, #e0e0e0);
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 18px;
      font-weight: bold;
      color: #333;
      cursor: pointer;
      user-select: none;
      letter-spacing: 2px;
      transition: all 0.3s;

      &:hover {
        background: linear-gradient(45deg, #e0e0e0, #d0d0d0);
        transform: scale(1.05);
      }
    }
  }

  .forgot-password-btn {
    width: 100%;
    height: 48px;
    font-size: 16px;
    font-weight: bold;
    border-radius: 8px;
    // background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;

    &:hover {
      // background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    }
  }
}

.forgot-password-links {
  text-align: center;
  margin-top: 20px;

  .link-btn {
    color: #666;
    font-size: 14px;
    padding: 0;
    cursor: pointer;

    &:hover {
      color: #409eff;
    }
  }

  .divider {
    margin: 0 12px;
    color: #ddd;
    font-size: 12px;
  }
}

.forgot-password-footer {
  text-align: center;
  margin-top: 30px;

  p {
    font-size: 12px;
    color: #999;
    margin: 0;
  }
}

// 响应式设计
@media (max-width: 480px) {
  .forgot-password-container {
    padding: 10px;
  }

  .forgot-password-box {
    padding: 30px 20px;
    min-height: auto;
  }

  .forgot-password-header {
    margin-bottom: 30px;

    .logo {
      width: 60px;
      height: 60px;
    }

    .title {
      font-size: 24px;
    }
  }
}
</style>
