<template>
  <div class="examine-content">
    <!-- <div class="notice-message">
      <div class="notice-desc">
        <b>注意：</b>请在设备上放置患者处方、认定表及相关资料后，点击一键审方！
      </div>
    </div> -->
    <div class="examine-center">
      <div class="examine-title">
        <img src="@/assets/images/doctor.png" />
        <div class="title">我是国药AI审方，很高兴见到你！</div>
      </div>
      <div class="btn-exam">
        <el-button
          size="large"
          type="primary"
          class="btn-style"
          @click="gotoCheck()"
          ><img
            src="@/assets/images/check.png"
            class="btn-icon"
          />一键审方</el-button
        >
      </div>
    </div>
  </div>
  <select-patient-information></select-patient-information>
</template>
<script setup>
import globalStore from '@/stores/modules/global';
const global = globalStore();
const gotoCheck = () => {
  global.selectPatientDialog = true;
};
</script>
<style lang="scss" scoped>
.examine-content {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;

  .examine-center {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1;
    background: #fff;
    margin: 12px;
    border-radius: 8px;
    background: #fff;
    box-shadow: 0px 0px 16px 0px rgba(21, 102, 80, 0.1);
  }

  .notice-message {
    box-sizing: border-box;
    background-color: rgb(245, 108, 108, 0.1);
    border-left: 5px solid #f56c6c;
    border-radius: 4px;
    // width: 100%;
    margin: 12px 12px 0 12px;
    display: flex;
    flex-direction: column;
    justify-content: center;

    .notice-desc {
      font-size: 16px;
      color: #666;
      margin: 16px;
    }
  }

  .examine-title {
    display: flex;
    align-items: center;
    gap: 20px;

    img {
      width: 50px;
    }

    .title {
      font-size: 28px;
    }
  }

  .btn-exam {
    margin: 60px 0 30px 0;

    .btn-style {
      padding: 26px 40px;
      font-size: 20px;

      .btn-icon {
        width: 26px;
        margin-right: 12px;
      }
    }
  }
}
</style>
