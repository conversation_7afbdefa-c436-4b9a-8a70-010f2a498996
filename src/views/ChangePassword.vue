<template>
  <div class="change-password-container">
    <div class="change-password-box">
      <div class="change-password-header">
        <img src="@/assets/images/logo.png" alt="logo" class="logo" />
        <h1 class="title">修改密码</h1>
        <!-- <p class="subtitle">请输入您的用户名和新密码</p> -->
      </div>

      <el-form
        ref="changePasswordFormRef"
        :model="changePasswordForm"
        :rules="changePasswordRules"
        class="change-password-form"
        @keyup.enter="handleChangePassword"
      >
        <el-form-item prop="oldPassword">
          <el-input
            v-model="changePasswordForm.oldPassword"
            type="password"
            placeholder="请输入原密码"
            size="large"
            prefix-icon="Lock"
            show-password
            clearable
          />
        </el-form-item>

        <el-form-item prop="newPassword">
          <el-input
            v-model="changePasswordForm.newPassword"
            type="password"
            placeholder="请输入新密码"
            size="large"
            prefix-icon="Key"
            show-password
            clearable
          />
        </el-form-item>

        <el-form-item prop="confirmPassword">
          <el-input
            v-model="changePasswordForm.confirmPassword"
            type="password"
            placeholder="请确认新密码"
            size="large"
            prefix-icon="Key"
            show-password
            clearable
          />
        </el-form-item>


        <el-form-item>
          <el-button
            type="primary"
            size="large"
            class="change-password-btn"
            :loading="loading"
            @click="handleChangePassword"
          >
            {{ loading ? "修改中..." : "确认修改" }}
          </el-button>
        </el-form-item>
      </el-form>

      <!-- 功能链接 -->
      <div class="change-password-links">
        <span @click="gotoLogin" class="link-btn">
          返回登录
        </span>
        <span class="divider">|</span>
        <span @click="gotoForgotPassword" class="link-btn">
          忘记密码
        </span>
      </div>

      <div class="change-password-footer">
        <p>© 2025 AI审方系统 - 智能药物审查解决方案</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive, ref } from "vue";
import { ElMessage } from "element-plus";
import router from "@/router";
import api from "@/api";

// 表单引用
const changePasswordFormRef = ref();

// 加载状态
const loading = ref(false);

// 验证码
const captchaCode = ref("");

// 修改密码表单数据
const changePasswordForm = reactive({
  username: "",
  oldPassword: "",
  newPassword: "",
  confirmPassword: "",
  captcha: "",
});

// 自定义验证规则
const validateConfirmPassword = (rule, value, callback) => {
  if (value === "") {
    callback(new Error("请确认新密码"));
  } else if (value !== changePasswordForm.newPassword) {
    callback(new Error("两次输入的密码不一致"));
  } else {
    callback();
  }
};

// 表单验证规则
const changePasswordRules = reactive({
  username: [
    { required: true, message: "请输入用户名", trigger: "blur" },
    {
      min: 3,
      max: 20,
      message: "用户名长度在 3 到 20 个字符",
      trigger: "blur",
    },
  ],
  oldPassword: [
    { required: true, message: "请输入原密码", trigger: "blur" },
    { min: 6, max: 20, message: "密码长度在 6 到 20 个字符", trigger: "blur" },
  ],
  newPassword: [
    { required: true, message: "请输入新密码", trigger: "blur" },
    { min: 6, max: 20, message: "密码长度在 6 到 20 个字符", trigger: "blur" },
    {
      pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{6,}$/,
      message: "密码必须包含大小写字母和数字",
      trigger: "blur",
    },
  ],
  confirmPassword: [
    { required: true, validator: validateConfirmPassword, trigger: "blur" },
  ],
  captcha: [
    { required: true, message: "请输入验证码", trigger: "blur" },
    { len: 4, message: "验证码长度为 4 位", trigger: "blur" },
  ],
});

// 生成随机验证码
const generateCaptcha = () => {
  const chars = "ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678";
  let result = "";
  for (let i = 0; i < 4; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
};

// 刷新验证码
const refreshCaptcha = () => {
  captchaCode.value = generateCaptcha();
};

// 处理修改密码
const handleChangePassword = async () => {
  if (!changePasswordFormRef.value) return;

  try {
    // 验证表单
    await changePasswordFormRef.value.validate();

    // 验证验证码
    if (
      changePasswordForm.captcha.toLowerCase() !==
      captchaCode.value.toLowerCase()
    ) {
      ElMessage.error("验证码错误");
      refreshCaptcha();
      return;
    }

    loading.value = true;

    // 调用修改密码API
    const response = await api.changePassword({
      username: changePasswordForm.username,
      oldPassword: changePasswordForm.oldPassword,
      newPassword: changePasswordForm.newPassword,
      captcha: changePasswordForm.captcha,
    });

    if (response.code === 200) {
      ElMessage.success("密码修改成功，请重新登录");
      // 跳转到登录页面
      router.push({ name: "login" });
    } else {
      ElMessage.error(response.message || "密码修改失败");
      refreshCaptcha();
    }
  } catch (error) {
    console.error("修改密码失败:", error);
    ElMessage.error("修改密码失败，请检查输入信息");
    refreshCaptcha();
  } finally {
    loading.value = false;
  }
};

// 跳转到登录页面
const gotoLogin = () => {
  router.push({ name: "login" });
};

// 跳转到忘记密码页面
const gotoForgotPassword = () => {
  router.push({ name: "forgotPassword" });
};

// 初始化验证码
onMounted(() => {
  refreshCaptcha();
});
</script>

<style lang="scss" scoped>
.change-password-container {
  height: 100vh;
  width: 100vw;
  background: url("@/assets/images/login_mainbg.jpg") 0 0 no-repeat;
  background-size: cover;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  box-sizing: border-box;
}

.change-password-box {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 40px;
  width: 100%;
  max-width: 420px;
  min-height: 600px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.change-password-header {
  text-align: center;
  margin-bottom: 40px;

  .logo {
    width: 80px;
    height: 80px;
    margin-bottom: 20px;
  }

  .title {
    font-size: 28px;
    font-weight: bold;
    color: #333;
    margin: 0 0 8px 0;
  }

  .subtitle {
    font-size: 14px;
    color: #666;
    margin: 0;
  }
}

.change-password-form {
  .el-form-item {
    margin-bottom: 24px;
  }

  .captcha-container {
    display: flex;
    gap: 12px;

    .captcha-input {
      flex: 1;
    }

    .captcha-code {
      width: 100px;
      height: 40px;
      background: linear-gradient(45deg, #f0f0f0, #e0e0e0);
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 18px;
      font-weight: bold;
      color: #333;
      cursor: pointer;
      user-select: none;
      letter-spacing: 2px;
      transition: all 0.3s;

      &:hover {
        background: linear-gradient(45deg, #e0e0e0, #d0d0d0);
        transform: scale(1.05);
      }
    }
  }

  .change-password-btn {
    width: 100%;
    height: 48px;
    font-size: 16px;
    font-weight: bold;
    border-radius: 8px;
    // background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;

    &:hover {
      // background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    }
  }
}

.change-password-links {
  text-align: center;
  margin-top: 20px;

  .link-btn {
    color: #666;
    font-size: 14px;
    padding: 0;
    cursor: pointer;
    &:hover {
      color: #409eff;
    }
  }

  .divider {
    margin: 0 12px;
    color: #ddd;
    font-size: 12px;
  }
}

.change-password-footer {
  text-align: center;
  margin-top: 30px;

  p {
    font-size: 12px;
    color: #999;
    margin: 0;
  }
}

// 响应式设计
@media (max-width: 480px) {
  .change-password-container {
    padding: 10px;
  }

  .change-password-box {
    padding: 30px 20px;
    min-height: auto;
  }

  .change-password-header {
    margin-bottom: 30px;

    .logo {
      width: 60px;
      height: 60px;
    }

    .title {
      font-size: 24px;
    }
  }
}
</style>
