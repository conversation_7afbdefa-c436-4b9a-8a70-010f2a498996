<template>
  <div class="examine-content">
    <div class="examine-center">
      <el-tooltip effect="dark" placement="top" content="结束审方">
        <el-icon class="circle-close" @click="finishTask()"
          ><CircleCloseFilled
        /></el-icon>
      </el-tooltip>
      <div class="examine-title">
        <img src="@/assets/images/doctor.png" />
        <div class="title">我是国药AI审方，很高兴见到你！</div>
      </div>
      <div class="breathing-text"> {{ global.description }}</div>
      <event-steps></event-steps>
    </div>
  </div>
  <!-- 患者匹配信息确认（初次） -->
  <first-reviewer-form
    v-if="
      global.activeIndex == 4 &&
      global.patientDialog &&
      global.firstCheckState === true
    "
  ></first-reviewer-form>
  <!-- 患者匹配信息确认（非初次） -->
  <reviewer-form
    v-if="
      global.activeIndex == 4 &&
      global.patientDialog &&
      global.firstCheckState === false
    "
  ></reviewer-form>
</template>
<script setup>
import globalStore from "@/stores/modules/global";
import websocketPrint from "@/hooks/websocketPrint";
import router from "@/router";
const { gotoState, finishTaskFun } = websocketPrint();
const global = globalStore();
onMounted(async () => {
  await gotoState();
});
const finishTask = () => {
  ElMessageBox.confirm("确定要结束审方吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    await finishTaskFun();
    router.replace({ name: "home" });
  });
};
</script>
<style lang="scss" scoped>
.examine-content {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;

  .examine-center {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1;
    background: #fff;
    margin: 12px;
    border-radius: 8px;
    background: #fff;
    box-shadow: 0px 0px 16px 0px rgba(21, 102, 80, 0.1);
  }

  .notice-message {
    box-sizing: border-box;
    background-color: rgb(245, 108, 108, 0.1);
    border-left: 5px solid #f56c6c;
    border-radius: 4px;
    // width: 100%;
    margin: 12px 12px 0 12px;
    display: flex;
    flex-direction: column;
    justify-content: center;

    .notice-desc {
      font-size: 16px;
      color: #666;
      margin: 16px;
    }
  }

  .examine-title {
    display: flex;
    align-items: center;
    gap: 20px;

    img {
      width: 50px;
    }

    .title {
      font-size: 28px;
    }
  }
}

@keyframes breathing {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.breathing-text {
  color: orange;
  font-size: 28px;
  margin: 100px 0 40px 0;
  animation: breathing 2s infinite; /* 持续时间2秒，无限循环 */
}
.circle-close {
  position: absolute;
  width: 30px;
  right: 30px;
  top: 30px;
  font-size: 30px;
  color: red;
  cursor: pointer;
}
</style>
