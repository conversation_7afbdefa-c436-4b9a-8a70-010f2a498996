import { createRouter, createWebHashHistory } from "vue-router";
import useAuthStore from "@/stores/modules/auth";
import { ElMessage } from "element-plus";
const routes = [
  {
    path: "/login",
    name: "login",
    meta: {
      title: "登录 - AI审方系统",
      keepAlive: false,
    },
    component: () => import("@/views/Login.vue"),
  },
  {
    path: "/change-password",
    name: "changePassword",
    meta: {
      title: "修改密码 - AI审方系统",
      keepAlive: false,
    },
    component: () => import("@/views/ChangePassword.vue"),
  },
  {
    path: "/forgot-password",
    name: "forgotPassword",
    meta: {
      title: "忘记密码 - AI审方系统",
      keepAlive: false,
    },
    component: () => import("@/views/ForgotPassword.vue"),
  },
  {
    path: "/main",
    name: "main",
    meta: {
      title: "AI审方系统",
      keepAlive: false,
    },
    component: () => import("@/views/MainLayout.vue"),
    children: [
      {
        path: "",
        name: "home",
        meta: {
          title: "AI审方",
          keepAlive: false,
        },
        component: () => import("@/views/Home.vue"),
      },
      {
        path: "detail/:id",
        name: "detail",
        meta: {
          title: "审方结果",
          keepAlive: false,
        },
        component: () => import("@/views/Detail.vue"),
      },
      {
        path: "progress/:id",
        name: "progress",
        meta: {
          title: "审方进度",
          keepAlive: false,
        },
        component: () => import("@/views/Progress.vue"),
      },
    ],
  },
  {
    path: "/",
    redirect: "/main",
  },
];

const router = createRouter({
  history: createWebHashHistory("/"),
  routes,
  scrollBehavior: () => ({ left: 0, top: 0 }),
});
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore();

  // 不需要登录的页面
  const publicPages = ["/login", "/change-password", "/forgot-password"];
  const isPublicPage = publicPages.includes(to.path);

  // 如果是公开页面，直接通过
  if (isPublicPage) {
    next();
    return;
  }

  // 检查是否已登录
  if (!authStore.isLoggedIn) {
    ElMessage.warning("请先登录");
    next("/login");
    return;
  }

  // 检查token是否有效
  if (!authStore.checkTokenValid()) {
    ElMessage.warning("登录已过期，请重新登录");
    // authStore.logout();
    next("/login");
    return;
  }
  if (to.matched.length === 0) {
    //如果未匹配到路由
    from.name ? next({ name: from.name }) : next("/"); //如果上级也未匹配到路由则跳转登录页面，如果上级能匹配到则转上级路由
  }
  next();
});
export default router;
