import { defineStore } from "pinia";
const useAuthStore = defineStore("auth", {
  state: () => ({
    token: "",
    userInfo: {},
    isLoggedIn: false,
  }),
  actions: {
    // 检测是否登录
    checkTokenValid() {
      return true;
    },
    // async login(params: any) {
    //   const res = await login(params);
    //   this.token = res.data.token;
    //   this.userInfo = res.data.userInfo;
    //   this.isLoggedIn = true;
    // },
    // async logout() {
    //   await logout();
    //   this.token = "";
    //   this.userInfo = {};
    //   this.isLoggedIn = false;
    // }
  },
  persist: true, // 开启数据持久化
});
export default useAuthStore;
