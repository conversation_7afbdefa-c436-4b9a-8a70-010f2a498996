import { defineStore } from "pinia";
import router from "@/router";
const globalStore = defineStore("global", {
  state: () => ({
    name: "",
    activeIndex: 0,
    description: '',
    currentId: "", // 当前激活的匹配id
    firstCheckState: true, // 是否是初次认定
    patientDialog: false, // 患者匹配弹窗是否打开
    selectPatientDialog: false, // 选择患者弹窗是否打开
    leftList: <any>[
      // 左边导航栏
    ],
    // timeId: <any>null, // 全局定时查询状态
  }),
  actions: {
    logout() {
      localStorage.removeItem("userInfo");
      localStorage.removeItem("token");
      this.$reset();
      router.replace("/login");
    }
  },
  persist: true, // 开启数据持久化
});
export default globalStore;
