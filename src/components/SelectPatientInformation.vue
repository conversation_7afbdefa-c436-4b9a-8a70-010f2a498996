<template>
  <el-dialog
    title="患者信息"
    v-model="global.selectPatientDialog"
    @close="closeDialog"
    @open="openDialog"
    width="60%"
  >
    <div class="m-b12px flex">
      <el-input
        v-model="search"
        placeholder="患者姓名/身份证号"
        class="m-r-12px width-200"
        style="width: 200px"
        clearable
        @keyup.enter="searchTableData()"
        @clear="searchTableData()"
      ></el-input>
      <el-button
        type="primary"
        @click="searchTableData()"
        :loading="firstBtnLoading"
        >查询</el-button
      >
      <div class="text-right flex-1">
        <el-tooltip
          placement="top"
          content="请在设备上放置患者处方、认定表及相关资料后，点击审核！"
        >
          <el-button type="success" @click="handleFirstReviewer"
            >首次审核</el-button
          >
        </el-tooltip>
      </div>
    </div>
    <el-table
      ref="multipleTable"
      :data="tableData"
      style="width: 100%"
      height="calc(100vh - 360px)"
      v-loading="loading"
      element-loading-text="数据加载中，请稍后..."
      row-key="id"
      border
      :show-overflow-tooltip="true"
      @selection-change="handleSelectionChange"
    >
      <!-- <el-table-column type="selection" width="55" :reserve-selection="true" /> -->
      <el-table-column prop="name" label="姓名" />
      <el-table-column prop="sex" label="性别" width="100" />
      <el-table-column prop="idCard" label="身份证号" />
      <el-table-column prop="mediGeneName" label="认定药品" />
      <el-table-column width="100" label="操作">
        <template #default="scope">
          <el-tooltip
            content="请在设备上放置患者处方后，点击审核！"
            placement="top"
          >
            <el-button
              type="primary"
              size="small"
              @click="handleEdit(scope.row)"
            >
              处方审核
            </el-button>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <div class="flex m-t-12px width-100% justify-end">
      <el-pagination
        v-model:current-page="page.pageNo"
        v-model:page-size="page.pageSize"
        :page-sizes="[10, 20, 30, 50]"
        background
        size="small"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-dialog>
</template>
<script setup>
import websocketPrintHook from "@/hooks/websocketPrint";
import globalStore from "@/stores/modules/global";
const global = globalStore();
const { oneKeyCheck } = websocketPrintHook();
const search = ref("");
const page = reactive({
  pageNum: 1,
  pageSize: 10,
});
const tableData = ref([]);
const total = ref(0);
const multipleTable = ref(null);
const loading = ref(false);
const selectInfo = ref([]);
const handleSelectionChange = (val) => {
  selectInfo.value = val;
};
const getTableData = () => {
  let params = {
    pageNum: page.pageNum,
    pageSize: page.pageSize,
    search: search.value,
  };
  loading.value = true;
  setTimeout(() => {
    tableData.value = [
      {
        name: "张三",
        sex: "男",
        idCard: "110101199001011234",
        mediGeneName: "甲硝唑",
      },
    ];
    loading.value = false;
  }, 1000);
};
// 初次审方
const firstBtnLoading = ref(false);
const handleFirstReviewer = async () => {
  firstBtnLoading.value = true;
  try {
    await oneKeyCheck();
  } catch (error) {
  } finally {
    firstBtnLoading.value = false;
    global.selectPatientDialog = false;
  }
};
// 处方审核
const reviewBtnLoading = ref(false);
const handleEdit = async () => {
  reviewBtnLoading.value = true;
  try {
    await oneKeyCheck();
  } catch (error) {
  } finally {
    reviewBtnLoading.value = false;
    global.selectPatientDialog = false;
  }
};
const handleSizeChange = (size) => {
  page.pageNum = 1;
  page.pageSize = size;
  getTableData();
};
const handleCurrentChange = (page) => {
  page.pageNum = page;
  getTableData();
};
onMounted(() => {
  // 防止弹窗处于打开状态，刷新界面，数据丢失。
  getTableData();
});
const openDialog = () => {
  getTableData();
};
const closeDialog = () => {
  global.selectPatientDialog = false;
};
const searchTableData = () => {
  page.pageNum = 1;
  getTableData();
};
</script>
