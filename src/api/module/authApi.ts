import request from "@/utils/request";

// 登录接口参数类型
export interface LoginParams {
  username: string;
  password: string;
  captcha: string;
}

// 登录响应类型
export interface LoginResponse {
  code: number;
  message: string;
  data: {
    token: string;
    userInfo: {
      id: string;
      username: string;
      name: string;
      role: string;
    };
  };
}

// 用户登录
export const login = (params: LoginParams): Promise<LoginResponse> => {
  return request({
    url: "/v0-1/auth/login",
    method: "post",
    data: params,
  });
};

// 用户登出
export const logout = (): Promise<any> => {
  return request({
    url: "/v0-1/auth/logout",
    method: "post",
  });
};

// 获取用户信息
export const getUserInfo = (): Promise<any> => {
  return request({
    url: "/v0-1/auth/userinfo",
    method: "get",
  });
};

// 刷新token
export const refreshToken = (): Promise<any> => {
  return request({
    url: "/v0-1/auth/refresh",
    method: "post",
  });
};

// 修改密码接口参数类型
export interface ChangePasswordParams {
  username: string;
  oldPassword: string;
  newPassword: string;
  captcha: string;
}

// 忘记密码接口参数类型
export interface ForgotPasswordParams {
  username: string;
  email: string;
  captcha: string;
}

// 修改密码
export const changePassword = (params: ChangePasswordParams): Promise<any> => {
  return request({
    url: "/v0-1/auth/change-password",
    method: "post",
    data: params,
  });
};

// 忘记密码
export const forgotPassword = (params: ForgotPasswordParams): Promise<any> => {
  return request({
    url: "/v0-1/auth/forgot-password",
    method: "post",
    data: params,
  });
};
