import { fileURLToPath, URL } from "node:url";
import { defineConfig, loadEnv, UserConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import vueDevTools from "vite-plugin-vue-devtools";
/* Elementplus组件按需导入 start */
import AutoImport from "unplugin-auto-import/vite";
import Components from "unplugin-vue-components/vite";
import { ElementPlusResolver } from "unplugin-vue-components/resolvers";
import { viteCommonjs } from "@originjs/vite-plugin-commonjs";
import UnoCSS from "unocss/vite";
const timeStamp = new Date().getTime(); //随机时间戳
/* ElementPlus组件按需导入 end */
export default defineConfig(({ mode }) => {
  // 配置环境
  const env = loadEnv(mode, process.cwd(), ["VITE_", "APP_"]);
  return {
    plugins: [
      vue(),
      viteCommonjs(),
      UnoCSS(),
      // vueDevTools(),
      /* elementPlus 按需导入组件 start */
      AutoImport({
        // 自动导入 Vue 相关函数，如：ref, reactive, toRef 等
        imports: [
          "vue",
          "vue-router", // 如果需要自动导入 vue-router 的函数
          "pinia", // 如果需要自动导入 pinia 的函数
        ],
        // 生成自动导入的 TypeScript 声明文件
        dts: "src/auto-imports.d.ts",
        // 生成 ESLint 配置
        eslintrc: {
          enabled: true, // 默认 false
          filepath: "./.eslintrc-auto-import.json", // 默认 ././eslintrc-auto-import.json
          globalsPropValue: true, // 默认 true (true | false | 'readonly' | 'readable' | 'writable' | 'writeable')
        },
        resolvers: [ElementPlusResolver()], // ElementPLus UI库按需导入
      }),
      Components({
        resolvers: [ElementPlusResolver()],
        /* 配置组件自动导入 start */
        dirs: ["src/components"], // 要自动导入的组件目录
        extensions: ["vue"], // 文件扩展名
        deep: true, // 是否搜索子目录
        dts: true, // 生成类型声明文件
        /* 配置组件自动导入 end */
      }),
      /* elementPlus 按需导入组件 end */
    ],
    resolve: {
      alias: {
        "@": fileURLToPath(new URL("./src", import.meta.url)),
      },
    },
    server: {
      hmr: true,
      proxy: {
        "/v0-1/prtask": {
          target: "http://10.6.10.33:8080",
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/v0-1\/prtask/, "/v0-1/prtask"),
        },
        "/v0-1/workstation": {
          target: "http://10.6.10.33:8090",
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/v0-1\/prtask/, "/v0-1/prtask"),
        },
      },
    },
    build: {
      sourcemap: false, // 输出.map文件,默认是false
      rollupOptions: {
        output: {
          chunkFileNames: `static/js/[name].[hash]${timeStamp}.js`,
          entryFileNames: `static/js/[name].[hash]${timeStamp}.js`,
          assetFileNames: `static/[ext]/[name].[hash]${timeStamp}.[ext]`,
        },
      },
    },
  };
});
